#!/usr/bin/env python3
"""
Test WebSocket next_step functionality
"""
import asyncio
import websockets
import json
import requests

BASE_URL = "http://localhost:8000"

async def test_next_step():
    print("Testing WebSocket next_step functionality...")
    
    # First, login and create a room
    login_response = requests.post(f"{BASE_URL}/api/token/", json={
        "username": "testuser",
        "password": "testpass123"
    })
    
    if login_response.status_code != 200:
        print("Login failed")
        return
    
    token = login_response.json()['access']
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get templates
    templates_response = requests.get(f"{BASE_URL}/api/events/templates/", headers=headers)
    if templates_response.status_code != 200:
        print("Failed to get templates")
        return
    
    templates = templates_response.json()
    if not templates:
        print("No templates available")
        return
    
    # Create room
    room_response = requests.post(f"{BASE_URL}/api/rooms/create/", 
                                 json={"template_id": templates[0]['id']}, 
                                 headers=headers)
    
    if room_response.status_code != 201:
        print(f"Room creation failed: {room_response.status_code}")
        return
    
    room_data = room_response.json()
    room_code = room_data['room_code']
    print(f"Room created: {room_code}")
    
    # Connect to WebSocket
    ws_url = f"ws://localhost:8000/ws/room/{room_code}/?token={token}"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("WebSocket connected")
            
            # Send next_step message
            message = {"action": "next_step", "payload": {}}
            await websocket.send(json.dumps(message))
            print("Sent next_step message")
            
            # Wait for response
            response = await asyncio.wait_for(websocket.recv(), timeout=10)
            data = json.loads(response)
            print(f"Received response: {data}")
            
            if data.get('type') == 'step_started':
                print("✓ Next step started successfully!")
                print(f"  Step type: {data['payload']['step_info']['step_type']}")
                print(f"  Room status: {data['payload']['room_status']}")
            elif data.get('type') == 'error':
                print(f"✗ Error: {data['payload']['message']}")
            else:
                print(f"? Unexpected response type: {data.get('type')}")
                
    except asyncio.TimeoutError:
        print("✗ Timeout waiting for WebSocket response")
    except Exception as e:
        print(f"✗ WebSocket error: {e}")

if __name__ == "__main__":
    asyncio.run(test_next_step())
