#!/usr/bin/env python3
"""
Simple test script to verify basic API functionality
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_user_registration():
    """Test user registration"""
    print("Testing user registration...")
    url = f"{BASE_URL}/api/register/"
    data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 201:
            print("✓ User registration successful")
            return True
        else:
            print(f"✗ User registration failed: {response.status_code}")
            print(response.text)
            return False
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Make sure the backend is running.")
        return False

def test_user_login():
    """Test user login"""
    print("Testing user login...")
    url = f"{BASE_URL}/api/token/"
    data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            if 'access' in token_data:
                print("✓ User login successful")
                return token_data['access']
            else:
                print("✗ Login response missing access token")
                return None
        else:
            print(f"✗ User login failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server")
        return None

def test_get_templates(token):
    """Test getting event templates"""
    print("Testing get event templates...")
    url = f"{BASE_URL}/api/events/templates/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            templates = response.json()
            print(f"✓ Got {len(templates)} templates")
            return templates
        else:
            print(f"✗ Get templates failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server")
        return None

def test_create_room(token, template_id):
    """Test creating a room"""
    print("Testing room creation...")
    url = f"{BASE_URL}/api/rooms/create/"
    headers = {"Authorization": f"Bearer {token}"}
    data = {"template_id": template_id}
    
    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 201:
            room_data = response.json()
            print(f"✓ Room created with code: {room_data.get('room_code')}")
            return room_data
        else:
            print(f"✗ Room creation failed: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server")
        return None

def main():
    print("Starting API tests...")
    print("=" * 50)
    
    # Test registration
    if not test_user_registration():
        print("Registration test failed, but continuing (user might already exist)")
    
    # Test login
    token = test_user_login()
    if not token:
        print("Login failed, cannot continue tests")
        sys.exit(1)
    
    # Test get templates
    templates = test_get_templates(token)
    if not templates:
        print("Get templates failed, cannot continue tests")
        sys.exit(1)
    
    # Test create room with first template
    if templates:
        template_id = templates[0]['id']
        room = test_create_room(token, template_id)
        if not room:
            print("Room creation failed")
            sys.exit(1)
    
    print("=" * 50)
    print("All basic API tests passed! ✓")

if __name__ == "__main__":
    main()
