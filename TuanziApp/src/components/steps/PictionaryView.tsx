import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, Button } from 'react-native';
import { PictionaryCanvas, PathData } from '../PictionaryCanvas';
import { Message } from '../../types';

// This component is now fully "dumb". It only renders what it's told to.
interface PictionaryViewProps {
  isDrawer: boolean;
  pictionaryState: { drawer: string; word: string; };
  paths: PathData[];
  messages: Message[];
  onDraw: (pathData: PathData) => void;
  // We will add chat handlers later if needed
}

export const PictionaryView: React.FC<PictionaryViewProps> = ({
  isDrawer,
  pictionaryState,
  paths,
  messages,
  onDraw,
}) => {
  // Chat input is now local to this component
  const [messageInput, setMessageInput] = useState('');

  const handleSendMessage = () => {
    // This logic should be moved to the parent if it needs to interact with WebSocket
    console.log("Guess:", messageInput);
    setMessageInput('');
  };

  return (
    <View style={styles.gameContainer}>
      <View style={styles.topInfoPanel}>
        <Text style={styles.infoText}>绘画者: {pictionaryState.drawer}</Text>
        {/* Timer logic will be re-added later */}
        <Text style={styles.infoText}>时间: 60s</Text>
      </View>
      <View style={styles.wordPanel}>
        <Text style={styles.wordText}>{pictionaryState.word}</Text>
        {isDrawer && <Text style={styles.roleText}>(你是绘画者)</Text>}
      </View>
      <View style={styles.mainArea}>
        <View style={styles.canvasContainer}>
          <PictionaryCanvas
            isDrawer={isDrawer}
            onDraw={onDraw}
            paths={paths}
          />
        </View>
        <View style={styles.chatContainer}>
          <FlatList
            style={styles.chatList}
            data={messages}
            renderItem={({ item }) => (
              <View style={styles.messageBubble}>
                <Text style={styles.messageSender}>{item.sender}:</Text>
                <Text style={styles.messageText}>{item.message}</Text>
              </View>
            )}
            keyExtractor={(item, index) => index.toString()}
            inverted
          />
          <View style={styles.inputArea}>
            <TextInput
              style={styles.chatInput}
              placeholder={isDrawer ? "绘画中..." : "输入你的猜测..."}
              value={messageInput}
              onChangeText={setMessageInput}
              editable={!isDrawer}
            />
            <Button title="发送" onPress={handleSendMessage} disabled={isDrawer} />
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    gameContainer: { flex: 1, width: '100%', alignItems: 'center' },
    topInfoPanel: { flexDirection: 'row', justifyContent: 'space-around', width: '100%', paddingVertical: 5 },
    infoText: { fontSize: 16, color: '#666' },
    wordPanel: { marginVertical: 5, alignItems: 'center' },
    wordText: { fontSize: 28, fontWeight: 'bold', letterSpacing: 8 },
    roleText: { fontSize: 14, color: 'gray', marginTop: 4 },
    mainArea: { flex: 1, width: '100%', flexDirection: 'row', padding: 10 },
    canvasContainer: { flex: 3, marginRight: 10 },
    chatContainer: { flex: 2, borderWidth: 1, borderColor: '#eee', borderRadius: 10, backgroundColor: '#f9f9f9' },
    chatList: { flex: 1, padding: 5 },
    inputArea: { flexDirection: 'row', padding: 5, borderTopWidth: 1, borderColor: '#eee' },
    chatInput: { flex: 1, height: 40, borderWidth: 1, borderColor: '#ccc', borderRadius: 20, paddingHorizontal: 10, marginRight: 5 },
    messageBubble: { padding: 8, marginVertical: 4 },
    messageSender: { fontWeight: 'bold' },
    messageText: { fontSize: 15 },
});
