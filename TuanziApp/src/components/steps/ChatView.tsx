import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, Button, KeyboardAvoidingView, Platform } from 'react-native';
import { useAuth } from '../../auth/AuthContext';
import { Message } from '../../types';

// Props that this component receives from RoomScreen
interface ChatViewProps {
  ws: React.RefObject<WebSocket | null>;
}

export const ChatView: React.FC<ChatViewProps> = ({ ws }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageInput, setMessageInput] = useState<string>('');

  // Effect for handling incoming WebSocket messages
  useEffect(() => {
    const socket = ws.current;
    if (!socket) return;

    const handleMessage = (e: any) => {
      const data = JSON.parse(e.data);
      // This view only cares about 'chat_message' type
      if (data.type === 'chat_message') {
        setMessages(prevMessages => [{ sender: data.sender, message: data.message }, ...prevMessages]);
      }
    };

    socket.addEventListener('message', handleMessage);

    // Cleanup: remove the event listener when the component unmounts
    return () => {
      socket.removeEventListener('message', handleMessage);
    };
  }, [ws]);

  const handleSendMessage = () => {
    if (ws.current?.readyState === WebSocket.OPEN && messageInput) {
      ws.current.send(JSON.stringify({
        action: 'send_message',
        payload: { message: messageInput }
      }));
      setMessageInput('');
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <Text style={styles.title}>自由讨论</Text>
      <FlatList
        style={styles.chatList}
        data={messages}
        renderItem={({ item }) => (
          <View style={item.sender === user?.username ? styles.myMessageBubble : styles.theirMessageBubble}>
            <Text style={styles.messageSender}>{item.sender}:</Text>
            <Text style={styles.messageText}>{item.message}</Text>
          </View>
        )}
        keyExtractor={(item, index) => index.toString()}
        inverted
      />
      <View style={styles.inputArea}>
        <TextInput
          style={styles.chatInput}
          placeholder="输入聊天消息..."
          value={messageInput}
          onChangeText={setMessageInput}
        />
        <Button title="发送" onPress={handleSendMessage} />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, width: '100%' },
    title: { fontSize: 20, fontWeight: 'bold', textAlign: 'center', padding: 10 },
    chatList: { flex: 1, paddingHorizontal: 10 },
    inputArea: { flexDirection: 'row', padding: 10, borderTopWidth: 1, borderColor: '#ccc' },
    chatInput: { flex: 1, height: 40, borderWidth: 1, borderColor: 'gray', borderRadius: 20, paddingHorizontal: 15, marginRight: 10 },
    myMessageBubble: { backgroundColor: '#dcf8c6', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-end', marginRight: 10 },
    theirMessageBubble: { backgroundColor: '#f0f0f0', padding: 10, borderRadius: 15, marginVertical: 4, maxWidth: '80%', alignSelf: 'flex-start', marginLeft: 10 },
    messageSender: { fontWeight: 'bold', marginBottom: 3 },
    messageText: { fontSize: 16 },
});
