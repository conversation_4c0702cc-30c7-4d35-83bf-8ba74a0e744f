import React from 'react';
import { View, Text, Button, StyleSheet, ActivityIndicator } from 'react-native';

interface LobbyViewProps {
  isHost: boolean;
  onNextStep: () => void;
  isFinished?: boolean;
  // --- NEW: Prop to receive connection status ---
  isConnected?: boolean;
}

export const LobbyView: React.FC<LobbyViewProps> = ({ isHost, onNextStep, isFinished, isConnected }) => {
    return (
        <View style={styles.container}>
            {isFinished ? (
                <Text style={styles.statusText}>所有环节已结束！</Text>
            ) : (
                <>
                    <Text style={styles.statusText}>等待房主开始下一环节...</Text>
                    
                    {/* Display connection status indicator */}
                    {!isConnected && <ActivityIndicator style={styles.spinner} />}
                    
                    {isHost && (
                        <View style={styles.buttonContainer}>
                            {/* --- FIX: Disable button until connected --- */}
                            <Button 
                                title={isConnected ? "开始下一环节" : "正在连接..."} 
                                onPress={onNextStep}
                                disabled={!isConnected}
                            />
                        </View>
                    )}
                </>
            )}
            {/* Here we can display the list of participants and the scoreboard */}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    statusText: {
        fontSize: 18,
        color: '#666',
        marginBottom: 20,
    },
    buttonContainer: {
        width: '80%',
    },
    spinner: {
        marginBottom: 20,
    }
});
