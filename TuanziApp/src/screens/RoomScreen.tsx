import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, SafeAreaView, Alert, ActivityIndicator } from 'react-native';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { WEBSOCKET_URL_BASE } from '../api/client';
import { RootStackParamList, EventStep, Message } from '../types';

import { PictionaryView } from '../components/steps/PictionaryView';
import { ChatView } from '../components/steps/ChatView';
import { LobbyView } from '../components/steps/LobbyView';
import { PathData } from '../components/PictionaryCanvas';

interface PictionaryState {
  drawer: string;
  word: string;
}

type RoomScreenRouteProp = RouteProp<RootStackParamList, 'Room'>;

export const RoomScreen = () => {
    const route = useRoute<RoomScreenRouteProp>();
    const initialRoom = route.params.room;
    const { user, token } = useAuth();

    const [currentStep, setCurrentStep] = useState<EventStep | null>(null);
    const [_stepPayload, setStepPayload] = useState<any>(null);
    const [roomStatus, setRoomStatus] = useState(initialRoom.status);
    const [isWsConnected, setIsWsConnected] = useState(false);
    const [reconnectAttempts, setReconnectAttempts] = useState(0);
    const maxReconnectAttempts = 5;
    
    // All game-related states are now managed here
    const [messages, setMessages] = useState<Message[]>([]);
    const [paths, setPaths] = useState<PathData[]>([]);
    const [pictionaryState, setPictionaryState] = useState<PictionaryState | null>(null);
    
    const ws = useRef<WebSocket | null>(null);

    const connectWebSocket = useCallback(() => {
        if (!initialRoom || !token) return;

        const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${initialRoom.room_code}/?token=${token}`;
        ws.current = new WebSocket(wsUrl);

        ws.current.onopen = () => {
            console.log('WebSocket connected');
            setIsWsConnected(true);
            setReconnectAttempts(0); // Reset reconnect attempts on successful connection
        };

        ws.current.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            setIsWsConnected(false);

            // Attempt to reconnect if not a normal closure and we haven't exceeded max attempts
            if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
                const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff, max 30s
                console.log(`Attempting to reconnect in ${timeout}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);

                setTimeout(() => {
                    setReconnectAttempts(prev => prev + 1);
                    connectWebSocket();
                }, timeout);
            } else if (reconnectAttempts >= maxReconnectAttempts) {
                console.error('Max reconnection attempts reached');
                Alert.alert('连接失败', '无法连接到服务器，请检查网络连接后重新进入房间。');
            }
        };

        ws.current.onerror = (e: any) => {
            console.error('WebSocket error:', e.message);
        };

        ws.current.onmessage = (e: any) => {
            try {
                const data = JSON.parse(e.data);

                switch (data.type) {
                    case 'step_started':
                        setCurrentStep(data.payload.step_info);
                        setStepPayload(data.payload);
                        setRoomStatus(data.payload.room_status);
                        setMessages([]);
                        setPaths([]);
                        if (data.payload.step_info.step_type === 'GAME_PICTIONARY') {
                            setPictionaryState(data.payload);
                        }
                        break;
                    case 'round_over':
                        // Clear game state and return to lobby
                        setCurrentStep(null);
                        setStepPayload(null);
                        setRoomStatus('WAITING'); // Force back to waiting state
                        setPictionaryState(null);
                        setPaths([]); // Clear drawing paths
                        const alertTitle = data.payload.winner ? "猜对了!" : "时间到!";
                        const alertMessage = data.payload.winner
                            ? `${data.payload.winner} 猜中了答案: ${data.payload.word}`
                            : `时间到! 正确答案是: ${data.payload.word}`;
                        Alert.alert(alertTitle, alertMessage);
                        break;
                    case 'event_finished':
                        Alert.alert("活动结束", data.payload.message);
                        setRoomStatus('FINISHED');
                        setCurrentStep(null);
                        break;
                    case 'error':
                        Alert.alert("操作失败", data.payload.message);
                        break;
                    case 'chat_message':
                        setMessages(prev => [data.payload, ...prev]);
                        break;
                    case 'drawing_data':
                        setPaths(prev => [...prev, data.payload.path_data]);
                        break;
                    default:
                        console.warn('Unknown message type:', data.type);
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
    }, [initialRoom, token, reconnectAttempts, maxReconnectAttempts]);

    useEffect(() => {
        connectWebSocket();
        return () => {
            if (ws.current) {
                ws.current.close(1000, 'Component unmounting');
            }
        };
    }, [connectWebSocket]);

    const sendWebSocketMessage = (action: string, payload: object = {}) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
            ws.current.send(JSON.stringify({ action, payload }));
        }
    };

    // --- FIX: Implement Optimistic Update for drawing ---
    const handleDraw = (pathData: PathData) => {
        // Add the path to the local state immediately for a smooth drawing experience.
        setPaths(prev => [...prev, pathData]);
        // Then, send it to the server to be broadcast to others.
        sendWebSocketMessage('send_drawing', { path_data: pathData });
    };

    const handleSendMessage = (message: string) => {
        sendWebSocketMessage('send_message', { message });
    };

    const renderCurrentStep = () => {
        if (roomStatus === 'FINISHED') return <LobbyView isFinished={true} />;

        if (!currentStep || roomStatus === 'WAITING') {
            return <LobbyView isHost={user?.username === initialRoom.host} onNextStep={() => sendWebSocketMessage('next_step')} isConnected={isWsConnected} />;
        }

        switch (currentStep.step_type) {
            case 'GAME_PICTIONARY':
                // --- FIX: Add a guard clause to prevent rendering with incomplete data ---
                if (!pictionaryState) {
                    return <View style={styles.container}><ActivityIndicator size="large" /></View>;
                }
                return (
                    <PictionaryView
                        isDrawer={user?.username === pictionaryState.drawer}
                        pictionaryState={pictionaryState}
                        paths={paths}
                        messages={messages}
                        onDraw={handleDraw} // Pass the new handler
                        onSendMessage={handleSendMessage} // Pass the message handler
                    />
                );
            case 'FREE_CHAT':
                // ChatView can be simplified as well, but we focus on Pictionary first
                return <ChatView ws={ws} />;
            default:
                return <LobbyView isHost={user?.username === initialRoom.host} onNextStep={() => sendWebSocketMessage('next_step')} isConnected={isWsConnected} />;
        }
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                <Text style={styles.title}>房间: {initialRoom.room_code}</Text>
                {renderCurrentStep()}
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: '#fff' },
    container: { flex: 1 },
    title: { fontSize: 24, fontWeight: 'bold', padding: 20, textAlign: 'center' },
});
