import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Alert, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// --- FIX: Correct the import path ---
// The correct path from 'src/screens/' to 'src/auth/' is one level up ('../').
import { useAuth } from '../auth/AuthContext';
import { getEventTemplates } from '../api/eventApi';
import { API_URL } from '../api/client';
import { EventTemplate, RootStackParamList } from '../types';
import { commonStyles } from '../styles/commonStyles';

type NavigationProp = StackNavigationProp<RootStackParamList, 'CreateRoom'>;

export const CreateRoomScreen = () => {
    const { token } = useAuth();
    const navigation = useNavigation<NavigationProp>();
    const [templates, setTemplates] = useState<EventTemplate[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const fetchTemplates = async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getEventTemplates(token);
            setTemplates(data);
        } catch (error) {
            Alert.alert('错误', '无法加载模板列表。');
        } finally {
            setIsLoading(false);
        }
    };

    useFocusEffect(useCallback(() => { fetchTemplates(); }, [token]));

    const handleSelectTemplate = async (templateId: number) => {
        if (!token) return;
        try {
            const response = await fetch(`${API_URL}/api/rooms/create/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                body: JSON.stringify({ template_id: templateId }),
            });
            const data = await response.json();
            if (response.ok) {
                navigation.replace('Room', { room: data });
            } else {
                Alert.alert('错误', '无法创建房间。');
            }
        } catch (error) {
            Alert.alert('错误', '创建房间时发生错误。');
        }
    };

    const renderItem = ({ item }: { item: EventTemplate }) => (
        <TouchableOpacity style={styles.templateCard} onPress={() => handleSelectTemplate(item.id)}>
            <Text style={styles.templateName}>{item.name}</Text>
            <Text style={styles.templateDescription}>{item.description}</Text>
        </TouchableOpacity>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={templates}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                ListHeaderComponent={<Text style={commonStyles.title}>选择一个活动流程</Text>}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    templateCard: { backgroundColor: '#f9f9f9', padding: 20, marginVertical: 8, marginHorizontal: 16, borderRadius: 10 },
    templateName: { fontSize: 18, fontWeight: 'bold' },
    templateDescription: { fontSize: 14, color: '#666', marginTop: 5 },
});
