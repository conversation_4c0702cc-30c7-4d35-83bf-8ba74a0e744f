import json
import random
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Room
from events.models import EventStep
from games.models import Game, PictionaryGame, PlayerScore
from games.words import WORD_LIST

# All @database_sync_to_async helper functions remain the same as the last complete version.
@database_sync_to_async
def update_scores(winner, drawer, room):
    winner_score, _ = PlayerScore.objects.get_or_create(room=room, player=winner)
    winner_score.score += 10; winner_score.save()
    drawer_score, _ = PlayerScore.objects.get_or_create(room=room, player=drawer)
    drawer_score.score += 5; drawer_score.save()
    scores = PlayerScore.objects.filter(room=room).order_by('-score')
    return {score.player.username: score.score for score in scores}

@database_sync_to_async
def get_pictionary_game(room_code):
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None

@database_sync_to_async
def end_pictionary_round(room_code):
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

@database_sync_to_async
def start_pictionary_for_step(room, step):
    participants = list(room.participants.all())
    if len(participants) < 2:
        return None, "至少需要2名玩家才能开始游戏。"
    drawer = random.choice(participants)
    word = random.choice(WORD_LIST)
    room.status = Room.STATUS_IN_PROGRESS
    room.save()
    game_session, _ = Game.objects.update_or_create(room=room, defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True})
    PictionaryGame.objects.update_or_create(game=game_session, defaults={'current_word': word, 'current_drawer': drawer})
    return {"drawer": drawer.username, "word": word, "room_status": room.status, "step_info": {"step_type": step.step_type, "order": step.order}}, None


class RoomConsumer(AsyncWebsocketConsumer):
    # connect, disconnect, receive, handle_next_step, handle_drawing_data, handle_round_timeout remain the same...
    async def connect(self):
        if not self.scope['user'] or not self.scope['user'].is_authenticated: await self.close(); return
        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'; await self.channel_layer.group_add(self.room_group_name, self.channel_name); await self.accept()
    async def disconnect(self, close_code):
        if hasattr(self, 'room_group_name'): await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
    async def receive(self, text_data):
        data = json.loads(text_data); action = data.get('action'); payload = data.get('payload', {})
        if action == 'next_step': await self.handle_next_step()
        elif action == 'send_message': await self.handle_chat_message(payload)
        elif action == 'send_drawing': await self.handle_drawing_data(payload)
    async def handle_next_step(self):
        user = self.scope['user']; room = await get_room_with_template(self.room_code)
        if not room or room.host != user: await self.send_error('只有房主才能开始下一环节。'); return
        next_step = await advance_to_next_step(room)
        if not next_step: await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_event_over'}); return
        if next_step.step_type == EventStep.STEP_GAME_PICTIONARY:
            game_data, error = await start_pictionary_for_step(room, next_step)
            if error: await self.send_error(error); return
            await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_start', 'payload': game_data})
        elif next_step.step_type == EventStep.STEP_FREE_CHAT:
            room.status = Room.STATUS_IN_PROGRESS; await database_sync_to_async(room.save)()
            await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_start', 'payload': {"room_status": room.status, "step_info": {"step_type": next_step.step_type, "order": next_step.order}}})
    async def handle_chat_message(self, payload):
        message = payload.get('message', '').strip(); user = self.scope['user']; game = await get_pictionary_game(self.room_code)
        if game and message.lower() == game.current_word.lower() and user.id != game.current_drawer.id:
            updated_scores = await update_scores(winner=user, drawer=game.current_drawer, room=game.game.room); new_room_status = await end_pictionary_round(self.room_code)
            if new_room_status: await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_round_over', 'payload': {'winner': user.username, 'word': game.current_word, 'room_status': new_room_status, 'scores': updated_scores,}})
        else: await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_chat_message', 'payload': {'message': message, 'sender': user.username}})
    async def handle_drawing_data(self, payload):
        await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_drawing_data', 'payload': {'path_data': payload.get('path_data')}})

    # --- NEW: Generic error handler ---
    async def send_error(self, message):
        """Sends an error message back to the originating client."""
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- BROADCAST HANDLERS ---
    async def broadcast_step_start(self, event):
        # --- FIX: Create a copy of the payload for each user ---
        payload = event['payload'].copy()
        step_type = payload.get('step_info', {}).get('step_type')
        if step_type == EventStep.STEP_GAME_PICTIONARY:
            if self.scope['user'].username != payload['drawer']:
                payload['word'] = " ".join(["_" for _ in payload['word']])
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event): await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))
    async def broadcast_drawing_data(self, event): await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))
    async def broadcast_round_over(self, event): await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))
    async def broadcast_event_over(self, event): await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))
